import React, { useState } from 'react';
import './Produk.css';

/**
 * Interface untuk item price list
 */
interface PriceListItem {
  merek: string;
  unitModel: string;
  price: string;
  notes?: string;
  warranty?: string;
  discount?: string;
}

/**
 * Interface untuk data produk
 */
interface Product {
  id: string;
  name: string;
  category: string;
  image: string;
  description: string;
  priceList: PriceListItem[];
}

/**
 * Props untuk komponen Produk
 */
interface ProdukProps {
  className?: string;
}

/**
 * Komponen Produk dengan layout grid responsif
 * Menampilkan daftar produk dalam bentuk card dengan fitur price list
 */
const Produk: React.FC<ProdukProps> = ({ className = '' }) => {
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);

  // Data dummy produk dengan struktur yang berbeda-beda
  const products: Product[] = [
    {
      id: 'compressor-1',
      name: 'COMPRESSOR',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=300&h=200&fit=crop',
      description: 'Kompressor AC berkualitas tinggi untuk berbagai jenis kendaraan',
      priceList: [
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000', warranty: '3 Bulan', notes: 'Warranty 3 Bulan' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000', discount: 'Diskon 50 %' },
        { merek: 'CAT', unitModel: '16HD', price: 'Rp. 5.600.000' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000', discount: 'Diskon 50 %' },
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000' },
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000', discount: 'Diskon 50 %' },
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000' },
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000' },
        { merek: 'SANNY', unitModel: 'SY215 STD', price: 'Rp. 5.780.000' },
        { merek: 'SANNY', unitModel: 'SY500', price: 'Rp. 6.000.000' }
      ]
    },
    {
      id: 'evaporator-1',
      name: 'EVAPORATOR',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
      description: 'Evaporator AC dengan teknologi terbaru untuk pendinginan optimal',
      priceList: [
        { merek: 'DENSO', unitModel: 'EV-200', price: 'Rp. 2.500.000', warranty: '2 Tahun' },
        { merek: 'VALEO', unitModel: 'EV-150', price: 'Rp. 2.200.000', discount: 'Diskon 15%' },
        { merek: 'BEHR', unitModel: 'EV-300', price: 'Rp. 2.800.000' },
        { merek: 'MAHLE', unitModel: 'EV-250', price: 'Rp. 2.600.000', warranty: '18 Bulan' }
      ]
    },
    {
      id: 'motor-blower-1',
      name: 'MOTOR BLOWER',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=200&fit=crop',
      description: 'Motor blower AC dengan daya tahan tinggi dan performa maksimal',
      priceList: [
        { merek: 'BOSCH', unitModel: 'MB-100', price: 'Rp. 1.200.000', warranty: '1 Tahun' },
        { merek: 'SIEMENS', unitModel: 'MB-150', price: 'Rp. 1.400.000', discount: 'Diskon 10%' },
        { merek: 'VALEO', unitModel: 'MB-200', price: 'Rp. 1.600.000' },
        { merek: 'DENSO', unitModel: 'MB-120', price: 'Rp. 1.300.000', warranty: '15 Bulan' },
        { merek: 'BEHR', unitModel: 'MB-180', price: 'Rp. 1.500.000' }
      ]
    },
    {
      id: 'condenser-1',
      name: 'CONDENSER',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=300&h=200&fit=crop',
      description: 'Kondenser AC dengan material berkualitas untuk transfer panas optimal',
      priceList: [
        { merek: 'DENSO', unitModel: 'CD-500', price: 'Rp. 3.200.000', warranty: '2 Tahun' },
        { merek: 'VALEO', unitModel: 'CD-400', price: 'Rp. 2.900.000', discount: 'Diskon 20%' },
        { merek: 'BEHR', unitModel: 'CD-600', price: 'Rp. 3.500.000' },
        { merek: 'MAHLE', unitModel: 'CD-450', price: 'Rp. 3.100.000', warranty: '18 Bulan' },
        { merek: 'NISSENS', unitModel: 'CD-350', price: 'Rp. 2.700.000' },
        { merek: 'DELPHI', unitModel: 'CD-550', price: 'Rp. 3.300.000', warranty: '2 Tahun' }
      ]
    },
    {
      id: 'expansion-valve-1',
      name: 'EXPANSION VALVE',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=300&h=200&fit=crop',
      description: 'Katup ekspansi AC untuk mengatur aliran refrigeran dengan presisi',
      priceList: [
        { merek: 'DENSO', unitModel: 'EX-100', price: 'Rp. 450.000', warranty: '1 Tahun' },
        { merek: 'VALEO', unitModel: 'EX-120', price: 'Rp. 480.000', discount: 'Diskon 5%' },
        { merek: 'BOSCH', unitModel: 'EX-150', price: 'Rp. 520.000' },
        { merek: 'SIEMENS', unitModel: 'EX-110', price: 'Rp. 460.000', warranty: '15 Bulan' }
      ]
    },
    {
      id: 'receiver-drier-1',
      name: 'RECEIVER DRIER',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=300&h=200&fit=crop',
      description: 'Filter drier AC untuk menjaga kebersihan sistem refrigeran',
      priceList: [
        { merek: 'DENSO', unitModel: 'RD-200', price: 'Rp. 350.000', warranty: '1 Tahun' },
        { merek: 'VALEO', unitModel: 'RD-180', price: 'Rp. 320.000', discount: 'Diskon 8%' },
        { merek: 'BEHR', unitModel: 'RD-220', price: 'Rp. 380.000' },
        { merek: 'MAHLE', unitModel: 'RD-190', price: 'Rp. 340.000', warranty: '18 Bulan' },
        { merek: 'NISSENS', unitModel: 'RD-160', price: 'Rp. 300.000' }
      ]
    },
    {
      id: 'pressure-switch-1',
      name: 'PRESSURE SWITCH',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=300&h=200&fit=crop',
      description: 'Saklar tekanan AC untuk proteksi sistem dari tekanan berlebih',
      priceList: [
        { merek: 'BOSCH', unitModel: 'PS-50', price: 'Rp. 180.000', warranty: '1 Tahun' },
        { merek: 'SIEMENS', unitModel: 'PS-60', price: 'Rp. 200.000', discount: 'Diskon 12%' },
        { merek: 'DENSO', unitModel: 'PS-55', price: 'Rp. 190.000' },
        { merek: 'VALEO', unitModel: 'PS-45', price: 'Rp. 170.000', warranty: '15 Bulan' }
      ]
    },
    {
      id: 'ac-hose-1',
      name: 'AC HOSE',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop',
      description: 'Selang AC berkualitas tinggi tahan tekanan dan suhu ekstrem',
      priceList: [
        { merek: 'GATES', unitModel: 'AH-300', price: 'Rp. 250.000', warranty: '2 Tahun' },
        { merek: 'DAYCO', unitModel: 'AH-280', price: 'Rp. 230.000', discount: 'Diskon 15%' },
        { merek: 'CONTINENTAL', unitModel: 'AH-320', price: 'Rp. 270.000' },
        { merek: 'GOODYEAR', unitModel: 'AH-290', price: 'Rp. 240.000', warranty: '18 Bulan' },
        { merek: 'PARKER', unitModel: 'AH-310', price: 'Rp. 260.000' }
      ]
    },
    {
      id: 'thermostat-1',
      name: 'THERMOSTAT',
      category: 'AC Parts',
      image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop',
      description: 'Thermostat AC untuk mengatur suhu dengan akurat',
      priceList: [
        { merek: 'HONEYWELL', unitModel: 'TH-100', price: 'Rp. 380.000', warranty: '2 Tahun' },
        { merek: 'JOHNSON', unitModel: 'TH-120', price: 'Rp. 420.000', discount: 'Diskon 10%' },
        { merek: 'DANFOSS', unitModel: 'TH-150', price: 'Rp. 450.000' },
        { merek: 'EMERSON', unitModel: 'TH-110', price: 'Rp. 390.000', warranty: '18 Bulan' }
      ]
    },
    {
      id: 'refrigerant-1',
      name: 'REFRIGERANT R134A',
      category: 'AC Chemicals',
      image: 'https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=300&h=200&fit=crop',
      description: 'Refrigeran R134A berkualitas tinggi untuk sistem AC kendaraan',
      priceList: [
        { merek: 'DUPONT', unitModel: 'R134A-500g', price: 'Rp. 85.000', warranty: '1 Tahun' },
        { merek: 'HONEYWELL', unitModel: 'R134A-500g', price: 'Rp. 90.000', discount: 'Diskon 5%' },
        { merek: 'CHEMOURS', unitModel: 'R134A-1kg', price: 'Rp. 160.000' },
        { merek: 'MEXICHEM', unitModel: 'R134A-500g', price: 'Rp. 80.000', warranty: '6 Bulan' },
        { merek: 'ARKEMA', unitModel: 'R134A-1kg', price: 'Rp. 170.000' }
      ]
    }
  ];

  /**
   * Fungsi untuk menangani klik tombol "Lihat Price List"
   * @param productId - ID produk yang dipilih
   */
  const handleViewPriceList = (productId: string) => {
    setSelectedProduct(selectedProduct === productId ? null : productId);
  };

  /**
   * Fungsi untuk mendapatkan produk yang dipilih
   */
  const getSelectedProduct = () => {
    return products.find(product => product.id === selectedProduct);
  };

  return (
    <div className={`produk-container min-h-screen bg-base-100 ${className}`}>
      {/* Header */}
      <div className="produk-header bg-gradient-to-r from-primary to-secondary text-white p-6">
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold mb-2">📦 Produk AC</h1>
          <p className="text-lg opacity-90">Temukan berbagai komponen AC berkualitas tinggi</p>
        </div>
      </div>

      <div className="container mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Grid Produk */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {products.map((product) => (
                <div
                  key={product.id}
                  className={`product-card card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 ${
                    selectedProduct === product.id ? 'ring-2 ring-primary' : ''
                  }`}
                >
                  {/* Gambar Produk */}
                  <figure className="px-4 pt-4">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="rounded-xl w-full h-48 object-cover"
                      loading="lazy"
                    />
                  </figure>

                  {/* Konten Card */}
                  <div className="card-body p-4">
                    <h2 className="card-title text-lg font-bold text-primary">
                      {product.name}
                    </h2>
                    <p className="text-sm text-base-content opacity-70 mb-2">
                      {product.description}
                    </p>
                    <div className="badge badge-secondary badge-sm mb-3">
                      {product.category}
                    </div>

                    {/* Tombol Lihat Price List */}
                    <div className="card-actions justify-end">
                      <button
                        className={`btn btn-primary btn-sm ${
                          selectedProduct === product.id ? 'btn-active' : ''
                        }`}
                        onClick={() => handleViewPriceList(product.id)}
                      >
                        {selectedProduct === product.id ? 'Tutup Price List' : 'Lihat Price List'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Tabel Price List */}
          <div className="lg:col-span-1">
            <div className={`price-list-container transition-all duration-500 ${
              selectedProduct ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
            }`}>
              {selectedProduct && getSelectedProduct() && (
                <div className="card bg-base-100 shadow-lg sticky top-4">
                  <div className="card-body p-4">
                    {/* Header Tabel */}
                    <div className="flex items-center gap-3 mb-4">
                      <img
                        src={getSelectedProduct()!.image}
                        alt={getSelectedProduct()!.name}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="text-xl font-bold text-primary">
                          {getSelectedProduct()!.name}
                        </h3>
                        <p className="text-sm text-base-content opacity-70">
                          Price List
                        </p>
                      </div>
                    </div>

                    {/* Tabel Price List */}
                    <div className="overflow-x-auto">
                      <table className="table table-zebra table-sm">
                        <thead>
                          <tr>
                            <th className="text-primary">Merek</th>
                            <th className="text-primary">Unit Model</th>
                            <th className="text-primary">Price</th>
                            <th className="text-primary">Notes</th>
                          </tr>
                        </thead>
                        <tbody>
                          {getSelectedProduct()!.priceList.map((item, index) => (
                            <tr key={index} className="hover">
                              <td className="font-medium">{item.merek}</td>
                              <td>{item.unitModel}</td>
                              <td className="font-bold text-success">{item.price}</td>
                              <td>
                                {item.warranty && (
                                  <div className="badge badge-info badge-xs mb-1">
                                    {item.warranty}
                                  </div>
                                )}
                                {item.discount && (
                                  <div className="badge badge-success badge-xs">
                                    {item.discount}
                                  </div>
                                )}
                                {item.notes && !item.warranty && !item.discount && (
                                  <span className="text-xs opacity-70">{item.notes}</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Produk;
