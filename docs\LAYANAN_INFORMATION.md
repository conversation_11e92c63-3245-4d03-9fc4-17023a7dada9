# Dokumentasi Komponen Layanan dan Information

## Overview

Dokumentasi ini menjelaskan implementasi komponen **Layanan** dan **Information** untuk aplikasi PWA PT Putera Wibowo Borneo.

## Komponen Layanan

### Deskripsi
Komponen Layanan menampilkan 3 layanan utama perusahaan dalam format kartu yang responsif:
- Air Conditioner Service
- Ban & Velg (Tyre)
- Fabrikasi & Welding

### Fitur Utama
- **Layout Grid Responsif**: Otomatis menyesuaikan jumlah kolom berdasarkan ukuran layar
- **Animasi Smooth**: Efek fade-in dengan stagger untuk setiap kartu
- **Hover Effects**: Transformasi dan shadow yang interaktif
- **Accessibility**: Support keyboard navigation dan screen readers
- **Consistent Header**: Menggunakan header yang sama dengan komponen Calendar

### Struktur File
```
src/components/
├── Layanan.tsx      # Komponen utama
├── Layanan.css      # Styling responsif
```

### Props Interface
```typescript
interface LayananProps {
  className?: string;
}
```

### Responsive Breakpoints
- **Mobile**: < 768px (1 kolom)
- **Tablet**: 768px - 1023px (auto-fit, min 300px)
- **Desktop**: 1024px - 1199px (auto-fit, min 350px)
- **Large Desktop**: ≥ 1200px (3 kolom tetap)

## Komponen Information

### Deskripsi
Komponen Information menampilkan informasi seputar PWB dengan layout split-screen:
- **Kiri**: Daftar informasi yang dapat di-scroll
- **Kanan**: Detail view dari informasi yang dipilih

### Fitur Utama
- **Reordering**: Item yang diklik otomatis pindah ke posisi teratas
- **Detail View**: Menampilkan konten lengkap di panel kanan
- **Interactive List**: Hover dan active states yang jelas
- **Smooth Animations**: Transisi yang halus untuk semua interaksi
- **Accessibility**: Full keyboard support dan ARIA labels

### Struktur File
```
src/components/
├── Information.tsx  # Komponen utama
├── Information.css  # Styling responsif
```

### Props Interface
```typescript
interface InformationProps {
  className?: string;
}

interface InformationItem {
  id: string;
  title: string;
  image: string;
  description: string;
  content: string;
  date: string;
  category: string;
}
```

### State Management
```typescript
const [informationList, setInformationList] = useState<InformationItem[]>(initialData);
const [selectedItem, setSelectedItem] = useState<InformationItem | null>(null);
```

### Responsive Behavior
- **Desktop**: Layout 2 kolom (50% - 50%)
- **Tablet**: Layout 1 kolom dengan height yang disesuaikan
- **Mobile**: Layout 1 kolom dengan optimasi touch interaction

## Navigation Pattern

### Implementasi
Kedua komponen menggunakan pattern navigasi yang konsisten:
- **Hanya HOME** yang menampilkan footer navigation
- **Semua halaman lain** hanya memiliki tombol "Kembali ke HOME"
- **Event-based navigation** menggunakan CustomEvent

### Code Implementation
```typescript
// App.tsx - Navigation Logic
{activeTab === 'home' && (
  <Navigation
    activeTab={activeTab}
    onTabChange={handleTabChange}
  />
)}

// Component - Back to Home
const navigateToHome = () => {
  window.dispatchEvent(new CustomEvent('navigateToHome'));
};
```

## Styling Guidelines

### Theme Consistency
- Menggunakan DaisyUI color variables: `oklch(var(--p))`, `oklch(var(--s))`, dll.
- Gradient backgrounds yang konsisten dengan Homepage
- Font family: 'Poppins' untuk konsistensi

### Animation System
```css
/* Fade In Up Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered Animation */
.layanan-card:nth-child(1) { animation-delay: 0.1s; }
.layanan-card:nth-child(2) { animation-delay: 0.2s; }
.layanan-card:nth-child(3) { animation-delay: 0.3s; }
```

### Accessibility Features
- **ARIA Labels**: Proper labeling untuk screen readers
- **Keyboard Navigation**: Tab, Enter, dan Space key support
- **Focus Indicators**: Visible outline untuk keyboard users
- **Semantic HTML**: Menggunakan `<article>`, `<section>`, `role` attributes

## Data Structure

### Layanan Data
```typescript
const layananData: LayananItem[] = [
  {
    id: 'ac-service',
    title: 'Air Conditioner Service',
    image: '/images/layanan/ac-service.jpg',
    description: 'General Service & Maintenance Auto Air Conditioner System',
    features: [
      'Alat support & service r/v, bus, dll',
      'Alat berat produksi Excavator, Dozer',
      // ... more features
    ]
  },
  // ... more services
];
```

### Information Data
```typescript
const informationData: InformationItem[] = [
  {
    id: 'sumpah-pemuda',
    title: 'Selamat Hari SUMPAH PEMUDA',
    image: '/images/information/sumpah-pemuda.jpg',
    description: 'Ucapan Selamat Hari Pancasila',
    content: 'Konten lengkap informasi...',
    date: '28 Oktober 2024',
    category: 'Peringatan Nasional'
  },
  // ... more information
];
```

## Performance Optimizations

### Image Handling
- **Fallback Images**: Otomatis menggunakan icon default jika gambar tidak ditemukan
- **Lazy Loading**: Dapat diimplementasikan untuk optimasi loading
- **Responsive Images**: Menggunakan object-fit untuk konsistensi aspect ratio

### CSS Optimizations
- **CSS Grid**: Untuk layout yang efisien dan responsif
- **Transform**: Menggunakan transform untuk animasi yang smooth
- **Cubic-bezier**: Easing functions yang natural

## Testing Checklist

### Functionality
- [ ] Navigation ke/dari HOME berfungsi
- [ ] Reordering di Information component bekerja
- [ ] Detail view menampilkan konten yang benar
- [ ] Responsive design di semua breakpoints
- [ ] Animasi berjalan smooth

### Accessibility
- [ ] Keyboard navigation berfungsi
- [ ] Screen reader compatibility
- [ ] Focus indicators visible
- [ ] ARIA labels appropriate

### Performance
- [ ] Loading time optimal
- [ ] Smooth animations (60fps)
- [ ] Memory usage reasonable
- [ ] No console errors

## Future Enhancements

### Possible Improvements
1. **Search & Filter**: Tambah fitur pencarian untuk Information
2. **Pagination**: Untuk handling data yang lebih banyak
3. **Lazy Loading**: Optimasi loading gambar
4. **Offline Support**: Cache data untuk offline access
5. **Analytics**: Track user interactions
6. **Share Feature**: Kemampuan share informasi
7. **Print Support**: CSS untuk print-friendly layout

### Technical Debt
- Implementasi proper error boundaries
- Unit testing dengan Jest/React Testing Library
- E2E testing dengan Playwright
- Performance monitoring
- SEO optimizations (meta tags, structured data)

## Maintenance Notes

### Regular Updates
- Update data informasi secara berkala
- Monitor performance metrics
- Update dependencies
- Review accessibility compliance
- Test pada device/browser baru

### Known Issues
- Placeholder images saat ini menggunakan gambar produk
- Perlu implementasi proper image optimization
- Belum ada error handling untuk network failures

---

**Last Updated**: 11 Agustus 2025  
**Version**: 1.0.0  
**Author**: Augment Agent
