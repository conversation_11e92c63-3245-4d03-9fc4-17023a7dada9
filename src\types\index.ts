/**
 * TypeScript interfaces untuk aplikasi PPWA
 */

/**
 * Interface untuk data layanan
 */
export interface LayananItem {
  id: string;
  title: string;
  image: string;
  description: string;
  features: string[];
}

/**
 * Interface untuk data informasi
 */
export interface InformationItem {
  id: string;
  title: string;
  image: string;
  description: string;
  content: string;
  date: string;
  category: string;
}

/**
 * Interface untuk props komponen Layanan
 */
export interface LayananProps {
  className?: string;
}

/**
 * Interface untuk props komponen Information
 */
export interface InformationProps {
  className?: string;
}

/**
 * Interface untuk navigation event
 */
export interface NavigationEvent extends CustomEvent {
  type: 'navigateToHome';
}

/**
 * Interface untuk kategori informasi
 */
export interface InformationCategory {
  id: string;
  name: string;
  color: string;
}

/**
 * Interface untuk meta informasi
 */
export interface MetaInfo {
  date: string;
  category: string;
  author?: string;
  tags?: string[];
}

/**
 * Interface untuk layanan feature
 */
export interface LayananFeature {
  id: string;
  text: string;
  icon?: string;
  highlighted?: boolean;
}

/**
 * Interface untuk extended layanan item dengan features yang lebih detail
 */
export interface ExtendedLayananItem extends Omit<LayananItem, 'features'> {
  features: LayananFeature[];
  price?: string;
  duration?: string;
  availability?: boolean;
}

/**
 * Interface untuk extended information item dengan metadata
 */
export interface ExtendedInformationItem extends InformationItem {
  meta: MetaInfo;
  readTime?: number;
  views?: number;
  likes?: number;
}

/**
 * Interface untuk search dan filter
 */
export interface SearchFilter {
  query: string;
  category?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'date' | 'title' | 'category' | 'views';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Interface untuk responsive breakpoints
 */
export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

/**
 * Interface untuk theme configuration
 */
export interface ThemeConfig {
  primary: string;
  secondary: string;
  accent: string;
  neutral: string;
  base: string;
  info: string;
  success: string;
  warning: string;
  error: string;
}

/**
 * Interface untuk animation configuration
 */
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  stagger?: number;
}

/**
 * Interface untuk component state
 */
export interface ComponentState {
  loading: boolean;
  error: string | null;
  data: any;
  lastUpdated: Date;
}

/**
 * Interface untuk API response
 */
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

/**
 * Interface untuk pagination
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Interface untuk paginated response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination;
}
