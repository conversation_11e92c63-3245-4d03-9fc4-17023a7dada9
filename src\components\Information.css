/* Information Component Styles - Responsive untuk semua ukuran layar */

/* Main container */
.information-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    oklch(var(--p) / 0.15) 0%,
    oklch(var(--s) / 0.1) 50%,
    oklch(var(--a) / 0.05) 100%);
  color: oklch(var(--bc));
  font-family: 'Poppins', sans-serif;
  /* Android/Web font consistency */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
}

/* Header styling - consistent with Calendar */
.information-header {
  width: 100%;
  max-width: 7xl;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Main content area */
.information-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem 2rem;
}

/* Layout container */
.information-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
  min-height: calc(100vh - 200px);
}

/* Left section - Information List */
.information-list-section {
  background: oklch(var(--b1));
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 10px 25px -5px oklch(var(--p) / 0.1);
  border: 1px solid oklch(var(--b3));
  overflow: hidden;
}

.information-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* Information item */
.information-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  background: oklch(var(--b2) / 0.5);
  position: relative;
  overflow: hidden;
}

.information-item:hover,
.information-item:focus {
  background: oklch(var(--p) / 0.05);
  border-color: oklch(var(--p) / 0.2);
  transform: translateX(4px);
  outline: 2px solid oklch(var(--p));
  outline-offset: 2px;
}

.information-item.active {
  background: oklch(var(--p) / 0.1);
  border-color: oklch(var(--p));
  box-shadow: 0 4px 12px oklch(var(--p) / 0.2);
}

.information-item-image {
  width: 80px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
  flex-shrink: 0;
}

.information-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.information-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.information-item-title {
  font-size: 1rem;
  font-weight: 600;
  color: oklch(var(--p));
  margin: 0;
  line-height: 1.3;
}

.information-item-description {
  font-size: 0.85rem;
  color: oklch(var(--bc) / 0.7);
  margin: 0;
  line-height: 1.4;
}

.information-item-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: oklch(var(--bc) / 0.6);
}

.information-item-date {
  font-weight: 500;
}

.information-item-category {
  background: oklch(var(--s) / 0.1);
  color: oklch(var(--s));
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* Right section - Detail View */
.information-detail-section {
  background: oklch(var(--b1));
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 10px 25px -5px oklch(var(--p) / 0.1);
  border: 1px solid oklch(var(--b3));
  overflow: hidden;
}

.information-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.information-detail-header {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.information-detail-image {
  width: 120px;
  height: 120px;
  border-radius: 0.75rem;
  object-fit: cover;
  flex-shrink: 0;
}

.information-detail-meta {
  flex: 1;
}

.information-detail-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: oklch(var(--p));
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.information-detail-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.information-detail-date {
  font-size: 0.9rem;
  color: oklch(var(--bc) / 0.7);
  font-weight: 500;
}

.information-detail-category {
  background: oklch(var(--s) / 0.1);
  color: oklch(var(--s));
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
}

.information-detail-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.information-detail-paragraph {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: oklch(var(--bc) / 0.8);
  font-size: 0.95rem;
}

.information-detail-paragraph:last-child {
  margin-bottom: 0;
}

/* Placeholder for when no item is selected */
.information-detail-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  color: oklch(var(--bc) / 0.5);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: oklch(var(--bc) / 0.7);
}

.placeholder-text {
  font-size: 1rem;
  line-height: 1.5;
  max-width: 300px;
  margin: 0 auto;
}

/* Navigation section */
.information-navigation {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* Back to home button */
.btn-back-home {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: oklch(var(--p));
  color: oklch(var(--pc));
  border: none;
  border-radius: 0.75rem;
  padding: 0.875rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px oklch(var(--p) / 0.3);
  font-family: inherit;
}

.btn-back-home:hover {
  background: oklch(var(--p) / 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px oklch(var(--p) / 0.4);
}

.btn-back-home:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px oklch(var(--p) / 0.3);
}

.btn-icon {
  font-size: 1.1rem;
}

/* Responsive Design */

/* Tablet Portrait */
@media screen and (max-width: 1023px) {
  .information-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .information-list {
    max-height: 400px;
  }

  .information-detail-section {
    min-height: 500px;
  }

  .information-item {
    padding: 0.875rem;
  }

  .information-item-image {
    width: 70px;
    height: 70px;
  }
}

/* Mobile */
@media screen and (max-width: 767px) {
  .information-main {
    padding: 0 0.75rem 1.5rem;
  }
  
  .information-layout {
    gap: 1rem;
  }
  
  .information-list-section,
  .information-detail-section {
    padding: 1rem;
    border-radius: 0.75rem;
  }
  
  .information-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .information-item-image {
    width: 60px;
    height: 60px;
  }
  
  .information-item-title {
    font-size: 0.9rem;
  }
  
  .information-item-description {
    font-size: 0.8rem;
  }
  
  .information-detail-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .information-detail-image {
    width: 100%;
    height: 200px;
  }
  
  .information-detail-title {
    font-size: 1.25rem;
  }
  
  .btn-back-home {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }
}

/* Scrollbar styling */
.information-list::-webkit-scrollbar,
.information-detail-content::-webkit-scrollbar {
  width: 6px;
}

.information-list::-webkit-scrollbar-track,
.information-detail-content::-webkit-scrollbar-track {
  background: oklch(var(--b3));
  border-radius: 3px;
}

.information-list::-webkit-scrollbar-thumb,
.information-detail-content::-webkit-scrollbar-thumb {
  background: oklch(var(--p) / 0.3);
  border-radius: 3px;
}

.information-list::-webkit-scrollbar-thumb:hover,
.information-detail-content::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--p) / 0.5);
}
